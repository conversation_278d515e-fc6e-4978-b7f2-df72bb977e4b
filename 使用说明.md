# SEO运营表格数据处理工具使用说明

## 功能介绍
本工具可以自动处理多个运营表格文件，实现以下功能：
1. 读取多个Excel文件和多个sheet表格
2. 汇总搜索关键词和单数数据
3. 按客服数量平均拆分数据
4. 保持原有的表格样式和格式

## 环境准备

### 1. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 2. 准备数据文件
将所有需要处理的Excel文件放在与脚本相同的目录下。

## 表格格式要求
Excel文件必须包含以下列：
- 搜索主图
- 商品ID  
- 搜索关键词
- 单数
- 合计单量
- 需求备注

## 使用步骤

### 1. 运行程序
```bash
python seo_data_processor.py
```

### 2. 程序执行流程
1. 自动扫描当前目录下的所有.xlsx文件
2. 读取每个文件的所有sheet
3. 提取和汇总搜索关键词数据
4. 显示数据汇总信息
5. 提示输入客服数量
6. 按客服数量平均拆分数据
7. 生成客服分配文件

### 3. 输出结果
程序会在`客服拆分结果`文件夹中生成：
- 客服1_分配表.xlsx
- 客服2_分配表.xlsx
- ...（根据客服数量）

## 拆分算法
使用贪心算法确保各客服分配的单数尽可能平均：
- 按关键词单数从大到小排序
- 每次将关键词分配给当前总单数最少的客服
- 确保工作量分配均衡

## 样式保持
程序会尽力保持原始表格的：
- 列宽设置
- 行高设置  
- 基本格式

## 注意事项
1. 确保Excel文件格式正确，包含必需的列
2. 搜索关键词列不能为空
3. 单数列必须为数字格式
4. 建议先备份原始文件
5. 如果遇到样式复制问题，数据内容不会受影响

## 故障排除
- 如果提示"未找到Excel文件"，请检查文件扩展名是否为.xlsx
- 如果某些sheet被跳过，请检查是否包含必需的列
- 如果样式复制失败，可以手动调整输出文件的格式

## 示例输出
```
=== SEO运营表格数据处理工具 ===
找到 3 个Excel文件
正在处理文件: 运营表格1.xlsx
  处理sheet: Sheet1
  处理sheet: 数据汇总
正在处理文件: 运营表格2.xlsx
  处理sheet: Sheet1

=== 数据汇总 ===
总共处理了 150 行数据
发现 45 个不同的搜索关键词
总单数: 1200

关键词汇总 (前10个):
  手机壳: 85
  充电器: 72
  数据线: 68
  ...

请输入客服数量: 3

=== 拆分结果 ===
客服1: 15个关键词, 总单数: 402
客服2: 15个关键词, 总单数: 398  
客服3: 15个关键词, 总单数: 400

已创建客服1的文件: 客服拆分结果/客服1_分配表.xlsx
已创建客服2的文件: 客服拆分结果/客服2_分配表.xlsx
已创建客服3的文件: 客服拆分结果/客服3_分配表.xlsx

处理完成！
```
