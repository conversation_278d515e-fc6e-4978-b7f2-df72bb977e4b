#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEO运营表格数据处理工具
功能：
1. 读取多个运营表格和多sheet表格
2. 汇总搜索关键词和单数
3. 平均拆分给不同客服
4. 保持原有样式和格式
"""

import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border
from openpyxl.drawing.image import Image
import os
import glob
import math
from collections import defaultdict
import shutil
from pathlib import Path

class SEODataProcessor:
    def __init__(self):
        self.data_summary = defaultdict(int)  # 存储关键词汇总数据
        self.all_data = []  # 存储所有原始数据
        self.required_columns = ['搜索主图', '商品ID', '搜索关键词', '单数', '合计单量', '需求备注']
        
    def read_excel_files(self, file_pattern="*.xlsx"):
        """读取所有Excel文件和多个sheet"""
        excel_files = glob.glob(file_pattern)
        
        if not excel_files:
            print("未找到Excel文件，请确保文件在当前目录下")
            return False
            
        print(f"找到 {len(excel_files)} 个Excel文件")
        
        for file_path in excel_files:
            print(f"正在处理文件: {file_path}")
            try:
                # 读取所有sheet
                excel_file = pd.ExcelFile(file_path)
                for sheet_name in excel_file.sheet_names:
                    print(f"  处理sheet: {sheet_name}")
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    # 检查必需的列是否存在
                    missing_cols = [col for col in self.required_columns if col not in df.columns]
                    if missing_cols:
                        print(f"    警告: sheet {sheet_name} 缺少列: {missing_cols}")
                        continue
                    
                    # 处理数据
                    self._process_sheet_data(df, file_path, sheet_name)
                    
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {str(e)}")
                
        return True
    
    def _process_sheet_data(self, df, file_path, sheet_name):
        """处理单个sheet的数据"""
        for index, row in df.iterrows():
            try:
                keyword = str(row['搜索关键词']).strip()
                quantity = int(row['单数']) if pd.notna(row['单数']) else 0
                
                if keyword and keyword != 'nan' and quantity > 0:
                    # 汇总关键词数量
                    self.data_summary[keyword] += quantity
                    
                    # 保存完整行数据
                    row_data = {
                        'file_path': file_path,
                        'sheet_name': sheet_name,
                        'row_index': index,
                        'data': row.to_dict()
                    }
                    self.all_data.append(row_data)
                    
            except Exception as e:
                print(f"处理行数据时出错 (文件: {file_path}, sheet: {sheet_name}, 行: {index}): {str(e)}")
    
    def print_summary(self):
        """打印汇总信息"""
        print("\n=== 数据汇总 ===")
        print(f"总共处理了 {len(self.all_data)} 行数据")
        print(f"发现 {len(self.data_summary)} 个不同的搜索关键词")
        
        total_quantity = sum(self.data_summary.values())
        print(f"总单数: {total_quantity}")
        
        print("\n关键词汇总 (前10个):")
        sorted_keywords = sorted(self.data_summary.items(), key=lambda x: x[1], reverse=True)
        for keyword, quantity in sorted_keywords[:10]:
            print(f"  {keyword}: {quantity}")
    
    def split_data_by_customer_service(self, num_customer_service):
        """按客服数量平均拆分数据"""
        if not self.data_summary:
            print("没有数据可以拆分")
            return []
        
        # 按关键词单数排序，便于平均分配
        sorted_keywords = sorted(self.data_summary.items(), key=lambda x: x[1], reverse=True)
        
        # 初始化客服分组
        customer_service_groups = [[] for _ in range(num_customer_service)]
        customer_service_totals = [0] * num_customer_service
        
        # 贪心算法分配：每次分配给当前总量最少的客服
        for keyword, quantity in sorted_keywords:
            min_index = customer_service_totals.index(min(customer_service_totals))
            customer_service_groups[min_index].append((keyword, quantity))
            customer_service_totals[min_index] += quantity
        
        print(f"\n=== 拆分结果 ===")
        for i, (group, total) in enumerate(zip(customer_service_groups, customer_service_totals)):
            print(f"客服{i+1}: {len(group)}个关键词, 总单数: {total}")
        
        return customer_service_groups
    
    def create_customer_service_files(self, customer_service_groups, output_dir="客服拆分结果"):
        """为每个客服创建Excel文件"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        for cs_index, keywords_group in enumerate(customer_service_groups):
            cs_keywords = {keyword for keyword, _ in keywords_group}
            
            # 筛选属于该客服的数据
            cs_data = [item for item in self.all_data 
                      if item['data']['搜索关键词'] in cs_keywords]
            
            if not cs_data:
                continue
            
            # 按原文件和sheet分组
            file_sheet_groups = defaultdict(list)
            for item in cs_data:
                key = (item['file_path'], item['sheet_name'])
                file_sheet_groups[key].append(item)
            
            # 为每个客服创建文件
            cs_filename = f"{output_dir}/客服{cs_index+1}_分配表.xlsx"
            self._create_excel_with_style(file_sheet_groups, cs_filename)
            
            print(f"已创建客服{cs_index+1}的文件: {cs_filename}")
    
    def _create_excel_with_style(self, file_sheet_groups, output_filename):
        """创建保持原样式的Excel文件"""
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            sheet_counter = 1
            
            for (original_file, original_sheet), data_items in file_sheet_groups.items():
                # 创建DataFrame
                df_data = [item['data'] for item in data_items]
                df = pd.DataFrame(df_data)
                
                # 确保列顺序正确
                df = df.reindex(columns=self.required_columns, fill_value='')
                
                # 写入sheet
                sheet_name = f"Sheet{sheet_counter}"
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 尝试复制原始样式
                try:
                    self._copy_sheet_style(original_file, original_sheet, 
                                         writer.book[sheet_name], df)
                except Exception as e:
                    print(f"复制样式时出错: {str(e)}")
                
                sheet_counter += 1
    
    def _copy_sheet_style(self, source_file, source_sheet, target_sheet, df):
        """尝试复制原始sheet的样式"""
        try:
            source_wb = openpyxl.load_workbook(source_file)
            source_ws = source_wb[source_sheet]
            
            # 复制列宽
            for col in source_ws.columns:
                col_letter = col[0].column_letter
                if col_letter in [cell.column_letter for cell in target_sheet[1]]:
                    target_sheet.column_dimensions[col_letter].width = \
                        source_ws.column_dimensions[col_letter].width
            
            # 复制行高
            for row_num in range(1, min(len(df) + 2, source_ws.max_row + 1)):
                if row_num in source_ws.row_dimensions:
                    target_sheet.row_dimensions[row_num].height = \
                        source_ws.row_dimensions[row_num].height
            
            source_wb.close()
            
        except Exception as e:
            print(f"复制样式详细错误: {str(e)}")

def main():
    """主函数"""
    print("=== SEO运营表格数据处理工具 ===")
    
    processor = SEODataProcessor()
    
    # 读取Excel文件
    if not processor.read_excel_files():
        return
    
    # 显示汇总信息
    processor.print_summary()
    
    # 获取客服数量
    while True:
        try:
            num_cs = int(input("\n请输入客服数量: "))
            if num_cs > 0:
                break
            else:
                print("客服数量必须大于0")
        except ValueError:
            print("请输入有效的数字")
    
    # 拆分数据
    cs_groups = processor.split_data_by_customer_service(num_cs)
    
    # 创建客服文件
    processor.create_customer_service_files(cs_groups)
    
    print("\n处理完成！")

if __name__ == "__main__":
    main()
